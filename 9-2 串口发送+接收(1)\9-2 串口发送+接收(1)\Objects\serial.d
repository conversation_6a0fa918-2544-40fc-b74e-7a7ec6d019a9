.\objects\serial.o: Hardware\Serial.c
.\objects\serial.o: .\Start\stm32f10x.h
.\objects\serial.o: .\Start\core_cm3.h
.\objects\serial.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\serial.o: .\Start\system_stm32f10x.h
.\objects\serial.o: .\User\stm32f10x_conf.h
.\objects\serial.o: .\Library\stm32f10x_adc.h
.\objects\serial.o: .\Start\stm32f10x.h
.\objects\serial.o: .\Library\stm32f10x_bkp.h
.\objects\serial.o: .\Library\stm32f10x_can.h
.\objects\serial.o: .\Library\stm32f10x_cec.h
.\objects\serial.o: .\Library\stm32f10x_crc.h
.\objects\serial.o: .\Library\stm32f10x_dac.h
.\objects\serial.o: .\Library\stm32f10x_dbgmcu.h
.\objects\serial.o: .\Library\stm32f10x_dma.h
.\objects\serial.o: .\Library\stm32f10x_exti.h
.\objects\serial.o: .\Library\stm32f10x_flash.h
.\objects\serial.o: .\Library\stm32f10x_fsmc.h
.\objects\serial.o: .\Library\stm32f10x_gpio.h
.\objects\serial.o: .\Library\stm32f10x_i2c.h
.\objects\serial.o: .\Library\stm32f10x_iwdg.h
.\objects\serial.o: .\Library\stm32f10x_pwr.h
.\objects\serial.o: .\Library\stm32f10x_rcc.h
.\objects\serial.o: .\Library\stm32f10x_rtc.h
.\objects\serial.o: .\Library\stm32f10x_sdio.h
.\objects\serial.o: .\Library\stm32f10x_spi.h
.\objects\serial.o: .\Library\stm32f10x_tim.h
.\objects\serial.o: .\Library\stm32f10x_usart.h
.\objects\serial.o: .\Library\stm32f10x_wwdg.h
.\objects\serial.o: .\Library\misc.h
.\objects\serial.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\serial.o: D:\keil5\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\serial.o: D:\keil5\ARM\ARMCC\Bin\..\include\string.h
