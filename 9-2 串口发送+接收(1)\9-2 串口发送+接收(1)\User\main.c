#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "Serial.h"
#include "ADC.h"
#include "Serial.h"
#include <string.h> 
#include <stdio.h>
#define USART1_RX_BUFFER_SIZE 64
char usart1_rx_line_buffer[USART1_RX_BUFFER_SIZE];
uint8_t usart1_rx_line_idx = 0;
volatile uint8_t usart1_line_received_flag = 0;
float temp_D_float_val=0.0f;

uint8_t temp_X;
uint8_t parsed_D_value;
float parsed_D_value_float;
int parsed_X_value = 0;

// 新增：存储不同类型的测量数据
float distance_value = 0.0f;
float triangle_value = 0.0f;
float circle_value = 0.0f;
float square_value = 0.0f;
char shape_type[20] = "NONE";

// 平均值计算相关变量
#define AVERAGE_BUFFER_SIZE 20  // 保存最近20个数据用于平均值计算
float distance_buffer[AVERAGE_BUFFER_SIZE];
float shape_buffer[AVERAGE_BUFFER_SIZE];
uint8_t distance_buffer_index = 0;
uint8_t shape_buffer_index = 0;
uint8_t distance_buffer_count = 0;
uint8_t shape_buffer_count = 0;

// 显示相关变量
float displayed_distance = 0.0f;    // 当前显示的距离
float displayed_shape_size = 0.0f;  // 当前显示的图形尺寸
uint8_t start_button_pressed = 0;   // 开始按键按下标志

// 用于检测值变化的变量
float last_distance_value = -1.0f;
float last_shape_value = -1.0f;

// 平均值计算函数
void add_distance_sample(float value) {
    distance_buffer[distance_buffer_index] = value;
    distance_buffer_index = (distance_buffer_index + 1) % AVERAGE_BUFFER_SIZE;
    if (distance_buffer_count < AVERAGE_BUFFER_SIZE) {
        distance_buffer_count++;
    }
}

void add_shape_sample(float value) {
    shape_buffer[shape_buffer_index] = value;
    shape_buffer_index = (shape_buffer_index + 1) % AVERAGE_BUFFER_SIZE;
    if (shape_buffer_count < AVERAGE_BUFFER_SIZE) {
        shape_buffer_count++;
    }
}

float get_distance_average(void) {
    if (distance_buffer_count == 0) return 0.0f;

    float sum = 0.0f;
    for (uint8_t i = 0; i < distance_buffer_count; i++) {
        sum += distance_buffer[i];
    }
    return sum / distance_buffer_count;
}

float get_shape_average(void) {
    if (shape_buffer_count == 0) return 0.0f;

    float sum = 0.0f;
    for (uint8_t i = 0; i < shape_buffer_count; i++) {
        sum += shape_buffer[i];
    }
    return sum / shape_buffer_count;
}

// 处理开始按键命令
void handle_start_button(void) {
    start_button_pressed = 1;

    // 强制添加当前距离值到缓冲区 - 无条件添加（完全模仿图形尺寸的处理方式）
    add_distance_sample(distance_value);
    char force_msg[100];
    sprintf(force_msg, "Force added distance %.2f to buffer (total: %d)\r\n",
            distance_value, distance_buffer_count);
    Serial_SendString(force_msg);

    // 计算当前平均值
    float avg_distance = get_distance_average();
    float avg_shape = get_shape_average();

    // 调试信息
    char debug_msg[100];
    sprintf(debug_msg, "Avg calculation: distance=%.2f (samples:%d), shape=%.2f (samples:%d)\r\n",
            avg_distance, distance_buffer_count, avg_shape, shape_buffer_count);
    Serial_SendString(debug_msg);

    // 更新显示值
    displayed_distance = avg_distance;
    displayed_shape_size = avg_shape;

    // 发送到HMI显示屏
    char hmi_cmd[50];

    // 更新距离显示 - 强制更新，即使为0（完全模仿图形尺寸的更新方式）
    int distance_int = (int)(avg_distance * 100);
    if(distance_int > 9999) distance_int = 9999;
    if(distance_int < 0) distance_int = 0;

    sprintf(hmi_cmd, "index.x2.val=%d\xff\xff\xff", distance_int);
    Serial2_SendString(hmi_cmd);
    Delay_ms(10);
    Serial2_SendString("ref index.x2\xff\xff\xff");
    Delay_ms(10);

    sprintf(hmi_cmd, "Button pressed! Distance updated: %.2f -> %d (avg of %d samples)\r\n",
            avg_distance, distance_int, distance_buffer_count);
    Serial_SendString(hmi_cmd);

    // 更新图形尺寸显示 - 强制更新，即使为0
    int shape_int = (int)(avg_shape * 100);
    if(shape_int > 9999) shape_int = 9999;
    if(shape_int < 0) shape_int = 0;

    sprintf(hmi_cmd, "index.x3.val=%d\xff\xff\xff", shape_int);
    Serial2_SendString(hmi_cmd);
    Delay_ms(10);
    Serial2_SendString("ref index.x3\xff\xff\xff");
    Delay_ms(10);

    sprintf(hmi_cmd, "Button pressed! Shape updated: %.2f -> %d (avg of %d samples)\r\n",
            avg_shape, shape_int, shape_buffer_count);
    Serial_SendString(hmi_cmd);

    // 如果没有图形数据，显示提示
    if(shape_buffer_count == 0) {
        Serial_SendString("Warning: No shape samples in buffer!\r\n");
    }

    // 显示当前所有数据状态
    char status_msg[200];
    sprintf(status_msg, "=== Current Data Status ===\r\n");
    Serial_SendString(status_msg);
    sprintf(status_msg, "distance_value: %.2f, triangle_value: %.2f, circle_value: %.2f, square_value: %.2f\r\n",
            distance_value, triangle_value, circle_value, square_value);
    Serial_SendString(status_msg);
    sprintf(status_msg, "Distance buffer: %d samples, Shape buffer: %d samples\r\n",
            distance_buffer_count, shape_buffer_count);
    Serial_SendString(status_msg);

    Serial_SendString("=== Display updated with average values ===\r\n");
}

uint8_t RxData;			//定义用于接收串口数据的变量
uint8_t x;
float I;
float P;
uint8_t D;
uint16_t ADValue;			//定义AD值变量
float Voltage;
float A;
char debug_serial1_buf[100];

// HMI更新函数 - 智能更新，只有值改变时才更新
void UpdateHMIDisplay(void)
{
	char hmi_cmd[100];
	uint8_t need_update = 0;

	// 收集距离数据到缓冲区 - 完全模仿图形尺寸的处理方式（移除>0条件）
	if(distance_value != last_distance_value) {
		add_distance_sample(distance_value);
		last_distance_value = distance_value;

		sprintf(hmi_cmd, "Distance sample: %.2f (buffer: %d/%d, avg: %.2f)\r\n",
		        distance_value, distance_buffer_count, AVERAGE_BUFFER_SIZE, get_distance_average());
		Serial_SendString(hmi_cmd);
	}

	// 收集图形尺寸数据到缓冲区（不立即显示）
	float shape_size = 0.0f;
	if(circle_value > 0) {
		shape_size = circle_value;  // 圆形直径
	} else if(triangle_value > 0) {
		shape_size = triangle_value;  // 三角形最大边长
	} else if(square_value > 0) {
		shape_size = square_value;  // 正方形边长
	}

	if(shape_size != last_shape_value && shape_size > 0.0f) {
		add_shape_sample(shape_size);
		last_shape_value = shape_size;

		sprintf(hmi_cmd, "Shape sample: %.2f (buffer: %d/%d, avg: %.2f)\r\n",
		        shape_size, shape_buffer_count, AVERAGE_BUFFER_SIZE, get_shape_average());
		Serial_SendString(hmi_cmd);
	}
}

// 强制更新HMI显示 - 在接收到新数据时立即调用
void ForceUpdateHMIDisplay(void)
{
	// 重置上次的值，强制更新
	last_distance_value = -1.0f;
	last_shape_value = -1.0f;
	UpdateHMIDisplay();
}

void HandleUsart1RxByte(uint8_t rx_byte)
{
    // 确保缓冲区不会溢出
    if (usart1_rx_line_idx < USART1_RX_BUFFER_SIZE - 1) // 留一个位置给字符串结束符 '\0'
    {
        if (rx_byte == '\n' || rx_byte == '\r') // 检测到行结束符 (换行符或回车符)
        {
            if (usart1_rx_line_idx > 0) // 确保在行结束符之前有实际数据
            {
                usart1_rx_line_buffer[usart1_rx_line_idx] = '\0'; // 字符串以 null 结尾
                usart1_line_received_flag = 1;                   // 设置标志，通知主循环有完整行数据待处理
                Serial_SendString(">>> Line complete flag set! <<<\r\n"); // 调试信息
            }
            // 不重置索引，让主循环处理完后再重置
        }
        else if (rx_byte >= 32 && rx_byte <= 126) // 只接收可打印字符
        {
            usart1_rx_line_buffer[usart1_rx_line_idx++] = rx_byte; // 将接收到的字节存入缓冲区
        }
        // 忽略其他控制字符
    }
    else // 缓冲区已满，重置以防止溢出，并丢弃当前行（可选：可以添加错误处理）
    {
        usart1_rx_line_idx = 0;
        Serial_SendString("USART1 RX Buffer Overflow!\r\n"); // 调试信息
    }
}

int main(void)
{
	/*系统初始化延时*/
	Delay_ms(100); //等待系统稳定

	/*禁用JTAG，释放PB3、PB4、PA15引脚*/
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_AFIO, ENABLE); //开启AFIO时钟
	GPIO_PinRemapConfig(GPIO_Remap_SWJ_JTAGDisable, ENABLE); //禁用JTAG，保留SWD

	/*串口初始化*/
	Serial_Init(); //串口初始化
	Delay_ms(50); //初始化间隔

	// 初始化串口2用于与串口屏通信
	Serial2_Init();
	Delay_ms(10); //初始化间隔
	// AD_Init(); //ADC初始化 - 暂时注释掉

	char String[100];
	uint32_t counter = 0; //计数器用于连续发送

	Delay_ms(100); //等待初始化完成
	Serial_SendString("STM32 Started!\r\n");
	Serial_SendString("System Clock: 8MHz HSI\r\n");
	Serial_SendString("JTAG Disabled, SWD Enabled\r\n");
	Serial_SendString("Waiting for Camera Data on USART1...\r\n");
	Serial_SendString("Supported formats:\r\n");
	Serial_SendString("  @DISTANCE:xx.xx -> index.x2.val (距离D)\r\n");
	Serial_SendString("  @TRIANGLE:xx.xx -> index.x3.val (X)\r\n");
	Serial_SendString("  @CIRCLE:xx.xx   -> index.x3.val (X)\r\n");
	Serial_SendString("  @SQUARE:xx.xx   -> index.x3.val (X)\r\n");
	Serial_SendString("  @NO_DETECTION:0.00 -> clear display\r\n");
	Serial_SendString("  @123.45 (legacy) -> index.x2.val (距离D)\r\n");

	// 测试新的HMI控件 (x2和x3)
	Serial_SendString("=== 测试新的HMI控件 ===\r\n");

	// 测试距离D位置 (x2控件)
	Serial_SendString("测试距离D位置 (x2控件)...\r\n");
	Serial2_SendString("index.x2.val=1234\xff\xff\xff");
	Delay_ms(500);

	// 测试X位置 (x3控件)
	Serial_SendString("测试X位置 (x3控件)...\r\n");
	Serial2_SendString("index.x3.val=5678\xff\xff\xff");
	Delay_ms(500);

	Serial_SendString("请观察:\r\n");
	Serial_SendString("- 距离D位置是否显示 1234\r\n");
	Serial_SendString("- X位置是否显示 5678\r\n");

	Delay_ms(3000);  // 等待3秒观察

	// 清零测试
	Serial_SendString("清零测试...\r\n");
	Serial2_SendString("index.x2.val=0\xff\xff\xff");
	Delay_ms(100);
	Serial2_SendString("index.x3.val=0\xff\xff\xff");
	Delay_ms(100);

	distance_value = 0.0f;
	triangle_value = 0.0f;
	Serial_SendString("=== HMI测试完成 ===\r\n");

	Serial_SendString("=== 系统就绪，等待摄像头数据和HMI按键 ===\r\n");

	while (1)
	{
		// 更频繁地更新显示，提高实时性
		if(counter % 1000 == 0) {  // 从5000改为1000，更频繁更新
			UpdateHMIDisplay();
		}

		// 简单的心跳测试 - 每10000次循环显示一次状态
		if(counter % 10000 == 0) {
			sprintf(String, "Heartbeat %lu, Shape: %s, Distance: %.2f, Circle: %.2f, Triangle: %.2f, Square: %.2f\r\n",
			        counter++, shape_type, distance_value, circle_value, triangle_value, square_value);
			Serial_SendString(String);
		}

		// 测试：每5000次循环模拟接收一次数据
//		if(counter == 5000) {
//			Serial_SendString("Simulating data reception...\r\n");
//			// 模拟接收 "@123.45"
//			HandleUsart1RxByte('@');
//			HandleUsart1RxByte('1');
//			HandleUsart1RxByte('2');
//			HandleUsart1RxByte('3');
//			HandleUsart1RxByte('.');
//			HandleUsart1RxByte('4');
//			HandleUsart1RxByte('5');
//			HandleUsart1RxByte('\n'); // 模拟换行符
//		}

		counter++;

		// 测试距离和图形数据：每20秒添加测试值，模仿摄像头数据
		if(counter % 2000 == 0 && counter > 0) {
			// 设置测试距离值
			distance_value = 12.34f + (counter / 2000) * 0.1f;
			// 设置测试图形值（圆形）
			circle_value = 5.67f + (counter / 2000) * 0.05f;

			Serial_SendString("=== Test Data Generated ===\r\n");
			char test_msg[150];
			sprintf(test_msg, "Test distance: %.2f, Test circle: %.2f\r\n",
			        distance_value, circle_value);
			Serial_SendString(test_msg);

			// 调用数据处理函数，模仿摄像头数据接收
			ForceUpdateHMIDisplay();
		}

		// 备选方案：如果HMI按键不工作，每30秒自动更新一次
		if(counter % 3000 == 0 && counter > 0) {
			Serial_SendString("=== Auto Update (30s) ===\r\n");
			handle_start_button();
		}

		// 暂时注释掉ADC相关代码
		// ADValue = AD_GetValue(); //获取AD转换的值
		// Voltage = (float)ADValue / 4095 * 3.3;
		// A = Voltage*10/0.5; //计算电流
		// sprintf(String, "ADC: %d, Voltage: %.2fV, Current: %.2fA\r\n", ADValue, Voltage, A);
		// Serial_SendString(String);

		// 检查串口2接收（HMI按键命令）
		if(Serial2_GetRxFlag() == 1) {
			uint8_t hmi_byte = Serial2_GetRxData();

			// 处理HMI按键 - 支持多种可能的格式
			static char hmi_buffer[20];
			static uint8_t hmi_index = 0;

			// 收集字符到缓冲区
			if(hmi_index < 19) {
				hmi_buffer[hmi_index++] = hmi_byte;
				hmi_buffer[hmi_index] = '\0';
			}

			// 检测多种可能的按键格式
			if(hmi_byte == 0x40 ||  // '@' 字符
			   hmi_byte == 0x31 ||  // '1' 字符
			   hmi_byte == 0x55 ||  // 'U' 字符
			   hmi_byte == '1'  ||  // ASCII '1'
			   hmi_byte == 'S'  ||  // 可能的 'Start'
			   strstr(hmi_buffer, "40") != NULL ||  // 包含 "40"
			   strstr(hmi_buffer, "31") != NULL ||  // 包含 "31"
			   strstr(hmi_buffer, "55") != NULL) {  // 包含 "55"

				Serial_SendString("=== HMI Button Pressed ===\r\n");
				handle_start_button();
				hmi_index = 0; // 重置缓冲区
			}

			// 缓冲区满时重置
			if(hmi_index >= 19) {
				hmi_index = 0;
			}
		}

		// 检查串口接收 - 使用中断处理的结果
		if(Serial_GetLineFlag() == 1) // 检查是否接收到完整行
		{
			usart1_line_received_flag = 0; // 清除标志位，表示已处理
			usart1_rx_line_idx = 0; // 重置索引，准备接收下一行
			Serial_SendString("=== RX from Camera ===\r\n");
			Serial_SendString("Raw data: ");
			char* line_data = Serial_GetLineData(); // 获取中断处理的数据
			Serial_SendString(line_data); // 打印整个接收缓冲区内容
			Serial_SendString("\r\n");
			uint8_t data_length = strlen(line_data);
			sprintf(debug_serial1_buf, "Data length: %d\r\n", data_length);
			Serial_SendString(debug_serial1_buf);

			// 处理摄像头发送的数据
			if (data_length > 0)
			{
				// 处理以@开头的摄像头数据（如@DISTANCE:123.45）
				if (line_data[0] == '@')
				{
					// 解析带类型标识的数据
					if (strncmp(&line_data[1], "DISTANCE:", 9) == 0)
					{
						if (sscanf(&line_data[10], "%f", &distance_value) == 1)
						{
							strcpy(shape_type, "DISTANCE");
							sprintf(debug_serial1_buf, "Parsed DISTANCE: %.2f\r\n", distance_value);
							Serial_SendString(debug_serial1_buf);

							// 收集数据到缓冲区，不立即显示
							ForceUpdateHMIDisplay();
						}
					}
					else if (strncmp(&line_data[1], "TRIANGLE:", 9) == 0)
					{
						if (sscanf(&line_data[10], "%f", &triangle_value) == 1)
						{
							strcpy(shape_type, "TRIANGLE");
							sprintf(debug_serial1_buf, "Parsed TRIANGLE: %.2f\r\n", triangle_value);
							Serial_SendString(debug_serial1_buf);

							// 收集数据到缓冲区，不立即显示
							ForceUpdateHMIDisplay();
						}
					}
					else if (strncmp(&line_data[1], "CIRCLE:", 7) == 0)
					{
						if (sscanf(&line_data[8], "%f", &circle_value) == 1)
						{
							strcpy(shape_type, "CIRCLE");
							sprintf(debug_serial1_buf, "Parsed CIRCLE: %.2f\r\n", circle_value);
							Serial_SendString(debug_serial1_buf);

							// 收集数据到缓冲区，不立即显示
							ForceUpdateHMIDisplay();

							sprintf(String, "Circle diameter: %.2f cm\r\n", circle_value);
							Serial_SendString(String);
						}
					}
					else if (strncmp(&line_data[1], "SQUARE:", 7) == 0)
					{
						if (sscanf(&line_data[8], "%f", &square_value) == 1)
						{
							strcpy(shape_type, "SQUARE");
							sprintf(debug_serial1_buf, "Parsed SQUARE: %.2f\r\n", square_value);
							Serial_SendString(debug_serial1_buf);

							// 收集数据到缓冲区，不立即显示
							ForceUpdateHMIDisplay();

							sprintf(String, "Square side: %.2f cm\r\n", square_value);
							Serial_SendString(String);
						}
					}
					else if (strncmp(&line_data[1], "NO_DETECTION:", 13) == 0)
					{
						strcpy(shape_type, "NO_DETECTION");
						distance_value = 0.0f;
						triangle_value = 0.0f;
						circle_value = 0.0f;
						square_value = 0.0f;
						Serial_SendString("No detection from camera\r\n");

						// 清零缓冲区
						distance_buffer_count = 0;
						shape_buffer_count = 0;
						distance_buffer_index = 0;
						shape_buffer_index = 0;
					}
					else
					{
						// 兼容原有的纯数字格式（如@123.45）
						float camera_data = 0.0f;
						if(sscanf(&line_data[1],"%f",&camera_data)==1)
						{
							distance_value = camera_data;
							triangle_value = 0.0f; // 清零三角形值
							strcpy(shape_type, "LEGACY");
							sprintf(debug_serial1_buf, "Parsed legacy format: %.2f\r\n", camera_data);
							Serial_SendString(debug_serial1_buf);

							// 收集数据到缓冲区，不立即显示
							ForceUpdateHMIDisplay();
						}
						else
						{
							Serial_SendString("Error: Failed to parse camera data\r\n");
						}
					}
				}
			}
		}

		Delay_ms(10); // 减少延时，提高响应速度
	}
}